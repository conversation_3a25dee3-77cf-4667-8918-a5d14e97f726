import axios, { AxiosInstance } from 'axios';
import { chunk } from 'lodash';
import { DateTime } from 'luxon';
import { inject, singleton } from 'tsyringe';

import { isFulfilled } from '@malou-io/package-utils';

import { Config } from ':config';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { Cache } from ':plugins/cache';
import { ProviderMetricsService } from ':providers/provider.metrics.service';
import { TheForkReviewDetailResponse } from ':providers/thefork/thefork-client-api.provider.types';

type TheForkAuthResponse = {
    access_token: string;
    expires_in: number;
    token_type: string;
    scope: string;
};

type TheForkReviewIdListResponse = {
    data: string[];
    hasNext: boolean;
};

@singleton()
export class TheForkClientApiProvider {
    private _apiInstance: AxiosInstance;

    constructor(
        private readonly _providerMetricsService: ProviderMetricsService,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {
        this._apiInstance = axios.create({
            baseURL: Config.platforms.lafourchette.clientApi.apiUrl,
        });
    }

    async getFreshToken(): Promise<string> {
        const authToken = await this._cache.get('the-fork-token');
        if (authToken) {
            return authToken;
        }
        const response = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'the-fork',
            requestId: 'the-fork.token.refresh',
            request: () =>
                axios.post<TheForkAuthResponse>(Config.platforms.lafourchette.clientApi.authUrl, {
                    audience: 'https://api.thefork.io',
                    grant_type: 'client_credentials',
                    client_id: Config.platforms.lafourchette.clientApi.clientId,
                    client_secret: Config.platforms.lafourchette.clientApi.clientSecret,
                }),
        });
        const tokenExpiryBufferInSeconds = 100; // We assume that the whole flow will not take more than 100s, so the token will remain valid all along
        const ttl = response.data.expires_in - tokenExpiryBufferInSeconds;
        await this._cache.set('the-fork-token', response.data.access_token, ttl);
        return response.data.access_token;
    }

    async getReviews(theForkInternalRestaurantId: string, recentOnly: boolean): Promise<TheForkReviewDetailResponse[]> {
        try {
            // TheFork API returns reviews from oldest to newest. We need to go until the last page to get the newest review
            // What we do is that we fetch the last 15 days of reviews if recentOnly is true, or the last 2 years otherwise
            const nowToISO = DateTime.now().toISODate();
            const startDateToIso = recentOnly
                ? DateTime.now().minus({ days: 15 }).toISODate()
                : DateTime.now().minus({ years: 2 }).toISODate();
            const token = await this.getFreshToken();

            const reviewCountPerPage = 100;
            const paginationLimit = 40; // i.e. 40 * 100 = 4 000 reviews max. To avoid looping indefinitely if there the flag hasNext is stuck at true
            const reviewIds: string[] = [];
            for (let i = 0; i < paginationLimit; i++) {
                const {
                    data: { data: ids, hasNext },
                } = await this._providerMetricsService.callAndTrackExternalAPI({
                    hostId: 'the-fork',
                    requestId: 'the-fork.reviews.list',
                    request: () =>
                        this._apiInstance<TheForkReviewIdListResponse>({
                            url: `/reviews`,
                            method: 'GET',
                            headers: {
                                Authorization: `Bearer ${token}`,
                            },
                            params: {
                                restaurantUuid: theForkInternalRestaurantId,
                                startDate: startDateToIso,
                                endDate: nowToISO,
                                limit: reviewCountPerPage,
                                page: i + 1,
                            },
                        }),
                });
                reviewIds.push(...ids);
                if (!hasNext) {
                    break;
                }
            }
            const chunkSize = 50; // We will send 50 requests at once to TheFork API
            const reviewChunks = chunk(reviewIds, chunkSize);
            const reviews: TheForkReviewDetailResponse[] = [];
            for (const reviewChunk of reviewChunks) {
                const response = await Promise.allSettled(
                    reviewChunk.map((reviewId) =>
                        this._providerMetricsService.callAndTrackExternalAPI({
                            hostId: 'the-fork',
                            requestId: 'the-fork.reviews.get',
                            request: () =>
                                this._apiInstance<TheForkReviewDetailResponse>({
                                    url: `/reviews/${reviewId}`,
                                    method: 'GET',
                                    headers: {
                                        Authorization: `Bearer ${token}`,
                                    },
                                }),
                        })
                    )
                );
                reviews.push(...response.filter(isFulfilled).map((r) => r.value.data));
            }
            logger.info('[THE_FORK_REVIEWS_OFFICIAL_API] Fetched TheFork reviews with client API', {
                reviewCount: reviews.length,
                theForkInternalRestaurantId,
            });
            return reviews;
        } catch (error) {
            logger.error('[THE_FORK_REVIEWS_OFFICIAL_API] Error fetching TheFork reviews', { error, theForkInternalRestaurantId });
            throw error;
        }
    }
}
