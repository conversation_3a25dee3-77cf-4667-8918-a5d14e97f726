import { LowerCaseCountryCode } from '@malou-io/package-utils';

export type TheForkReviewDetailResponse = {
    reviewUuid: string;
    restaurantUuid: string;
    isValid: boolean;
    createdTs: string;
    updatedTs: string;
    reservation: {
        reservationUuid: string;
        mealDate: string;
    };
    customer: {
        customerUuid: string;
        firstName: string;
        lastName: string;
    };
    dinerRating: {
        globalRating: number;
        ambienceRating: number;
        foodQualityRating: number;
        serviceRating: number;
        priceEvaluation: string;
        waitingTimeEvaluation: string;
        noiseLevelEvaluation: string;
    };
    comment?: {
        content: string;
        language: LowerCaseCountryCode | undefined;
        publishedTs: string;
    };
    experienceDetail: {
        occasion: string;
    };
    restaurantReply?: {
        content: string;
        language: string;
        publishedTs: string;
    };
};
